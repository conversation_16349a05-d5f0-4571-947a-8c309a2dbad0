{"schemaVersion": 1, "title": "SearchLeads Enrichment Output", "description": "Output data from the SearchLeads enrichment process", "type": "object", "properties": {"record_id": {"type": "string", "title": "Record ID", "description": "Unique identifier for the enrichment request"}, "enrichment_status": {"type": "string", "title": "Enrichment Status", "description": "Final status of the enrichment process (completed, failed, cancelled)"}, "file_name": {"type": "string", "title": "File Name", "description": "Name of the file containing enriched data"}, "enriched_records": {"type": ["string", "number"], "title": "Enriched Records", "description": "Number of records successfully enriched"}, "credits_involved": {"type": ["string", "number"], "title": "Credits Used", "description": "Number of credits consumed for this enrichment"}, "spreadsheet_url": {"type": "string", "title": "Spreadsheet URL", "description": "URL to access the enriched data spreadsheet"}, "progress_percentage": {"type": ["string", "number", "null"], "title": "Progress Percentage", "description": "Percentage of completion (if available)"}}}